#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VCP TextProcessor Plugin
文本处理插件 - 提供文本分析、翻译、摘要等功能
"""

import sys
import json
import re
from typing import Dict, Any, List
import os

# 尝试导入可选依赖
try:
    import jieba
    import jieba.analyse
    JIEBA_AVAILABLE = True
except ImportError:
    JIEBA_AVAILABLE = False

try:
    from langdetect import detect, detect_langs
    LANGDETECT_AVAILABLE = True
except ImportError:
    LANGDETECT_AVAILABLE = False

try:
    import textstat
    TEXTSTAT_AVAILABLE = True
except ImportError:
    TEXTSTAT_AVAILABLE = False


class TextProcessor:
    def __init__(self):
        self.max_length = int(os.getenv('max_text_length', '10000'))
        self.default_language = os.getenv('default_language', 'zh')
        self.enable_sentiment = os.getenv('enable_sentiment', 'true').lower() == 'true'
        
    def analyze_text(self, text: str) -> Dict[str, Any]:
        """分析文本的基本信息"""
        result = {
            "character_count": len(text),
            "word_count": len(text.split()),
            "line_count": len(text.split('\n')),
            "paragraph_count": len([p for p in text.split('\n\n') if p.strip()])
        }
        
        # 语言检测
        if LANGDETECT_AVAILABLE:
            try:
                detected_lang = detect(text)
                result["detected_language"] = detected_lang
                
                # 获取语言置信度
                lang_probs = detect_langs(text)
                result["language_probabilities"] = [
                    {"language": str(prob).split(':')[0], "confidence": float(str(prob).split(':')[1])}
                    for prob in lang_probs[:3]
                ]
            except:
                result["detected_language"] = "unknown"
        
        # 中文文本分析
        if JIEBA_AVAILABLE and any('\u4e00' <= char <= '\u9fff' for char in text):
            # 中文字符计数
            chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
            result["chinese_character_count"] = chinese_chars
            
            # 分词
            words = list(jieba.cut(text))
            result["chinese_word_count"] = len(words)
            result["unique_words"] = len(set(words))
        
        # 英文文本分析
        if TEXTSTAT_AVAILABLE:
            try:
                result["flesch_reading_ease"] = textstat.flesch_reading_ease(text)
                result["flesch_kincaid_grade"] = textstat.flesch_kincaid_grade(text)
                result["automated_readability_index"] = textstat.automated_readability_index(text)
            except:
                pass
        
        # 基本情感分析（简单版本）
        if self.enable_sentiment:
            result["sentiment_analysis"] = self._simple_sentiment_analysis(text)
        
        return result
    
    def extract_keywords(self, text: str, top_k: int = 10) -> List[str]:
        """提取关键词"""
        if JIEBA_AVAILABLE:
            # 使用 jieba 的 TF-IDF 算法
            keywords = jieba.analyse.extract_tags(text, topK=top_k, withWeight=False)
            return keywords
        else:
            # 简单的词频统计
            words = re.findall(r'\b\w+\b', text.lower())
            word_freq = {}
            for word in words:
                if len(word) > 2:  # 过滤短词
                    word_freq[word] = word_freq.get(word, 0) + 1
            
            # 返回频率最高的词
            sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
            return [word for word, freq in sorted_words[:top_k]]
    
    def format_text(self, text: str, format_type: str) -> str:
        """格式化文本"""
        if format_type == "uppercase":
            return text.upper()
        elif format_type == "lowercase":
            return text.lower()
        elif format_type == "title":
            return text.title()
        elif format_type == "remove_extra_spaces":
            return re.sub(r'\s+', ' ', text).strip()
        elif format_type == "remove_newlines":
            return text.replace('\n', ' ').replace('\r', '')
        elif format_type == "normalize_newlines":
            return re.sub(r'\r\n|\r|\n', '\n', text)
        elif format_type == "remove_punctuation":
            return re.sub(r'[^\w\s]', '', text)
        else:
            return text
    
    def summarize_text(self, text: str, max_sentences: int = 3) -> str:
        """简单的文本摘要（基于句子重要性）"""
        sentences = re.split(r'[.!?。！？]', text)
        sentences = [s.strip() for s in sentences if s.strip()]
        
        if len(sentences) <= max_sentences:
            return text
        
        # 简单的句子评分（基于长度和关键词）
        sentence_scores = []
        keywords = self.extract_keywords(text, top_k=20)
        
        for sentence in sentences:
            score = len(sentence)  # 基础分数：句子长度
            
            # 关键词加分
            for keyword in keywords:
                if keyword.lower() in sentence.lower():
                    score += 10
            
            sentence_scores.append((sentence, score))
        
        # 选择得分最高的句子
        top_sentences = sorted(sentence_scores, key=lambda x: x[1], reverse=True)[:max_sentences]
        
        # 按原文顺序排列
        result_sentences = []
        for sentence in sentences:
            if any(sentence == s[0] for s in top_sentences):
                result_sentences.append(sentence)
                if len(result_sentences) >= max_sentences:
                    break
        
        return '。'.join(result_sentences) + '。'
    
    def _simple_sentiment_analysis(self, text: str) -> Dict[str, Any]:
        """简单的情感分析"""
        positive_words = ['好', '棒', '优秀', '喜欢', '开心', '快乐', '满意', '赞', '爱', '美好', 'good', 'great', 'excellent', 'love', 'happy', 'wonderful']
        negative_words = ['坏', '差', '糟糕', '讨厌', '难过', '愤怒', '失望', '痛苦', '恨', 'bad', 'terrible', 'hate', 'sad', 'angry', 'disappointed']
        
        text_lower = text.lower()
        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)
        
        total_sentiment_words = positive_count + negative_count
        
        if total_sentiment_words == 0:
            sentiment = "neutral"
            confidence = 0.5
        elif positive_count > negative_count:
            sentiment = "positive"
            confidence = positive_count / total_sentiment_words
        elif negative_count > positive_count:
            sentiment = "negative"
            confidence = negative_count / total_sentiment_words
        else:
            sentiment = "neutral"
            confidence = 0.5
        
        return {
            "sentiment": sentiment,
            "confidence": round(confidence, 2),
            "positive_indicators": positive_count,
            "negative_indicators": negative_count
        }


def main():
    try:
        # 从标准输入读取参数
        input_data = sys.stdin.read().strip()
        if not input_data:
            raise ValueError("No input data received")
        
        params = json.loads(input_data)
        
        # 验证必需参数
        if 'command' not in params:
            raise ValueError("Missing required parameter: command")
        
        if 'text' not in params:
            raise ValueError("Missing required parameter: text")
        
        processor = TextProcessor()
        command = params['command']
        text = params['text']
        
        # 检查文本长度
        if len(text) > processor.max_length:
            raise ValueError(f"Text too long. Maximum length: {processor.max_length}")
        
        result = None
        
        if command == "analyze":
            result = processor.analyze_text(text)
            
        elif command == "extract_keywords":
            top_k = int(params.get('top_k', 10))
            keywords = processor.extract_keywords(text, top_k)
            result = {"keywords": keywords}
            
        elif command == "format":
            format_type = params.get('format_type', 'remove_extra_spaces')
            formatted_text = processor.format_text(text, format_type)
            result = {"formatted_text": formatted_text, "format_type": format_type}
            
        elif command == "summarize":
            max_sentences = int(params.get('max_sentences', 3))
            summary = processor.summarize_text(text, max_sentences)
            result = {"summary": summary, "original_length": len(text), "summary_length": len(summary)}
            
        elif command == "translate":
            # 这里可以集成翻译API，目前返回提示信息
            target_language = params.get('target_language', 'en')
            result = {
                "message": f"翻译功能需要配置翻译API。目标语言: {target_language}",
                "original_text": text,
                "target_language": target_language
            }
            
        else:
            raise ValueError(f"Unknown command: {command}")
        
        # 返回成功结果
        response = {
            "status": "success",
            "result": result,
            "messageForAI": f"文本处理完成。命令: {command}"
        }
        
        print(json.dumps(response, ensure_ascii=False, indent=2))
        
    except Exception as e:
        # 返回错误结果
        error_response = {
            "status": "error",
            "error": str(e),
            "messageForAI": f"文本处理失败: {str(e)}"
        }
        print(json.dumps(error_response, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main()
