#!/usr/bin/env node
/**
 * VCP SmartReminder Plugin
 * 智能提醒助手 - 支持自然语言时间解析和多种提醒方式
 */

const fs = require('fs').promises;
const path = require('path');
const schedule = require('node-schedule');
const moment = require('moment');
const chrono = require('chrono-node');

// 设置中文时区
moment.locale('zh-cn');

class SmartReminder {
    constructor() {
        this.remindersFile = path.join(__dirname, 'reminders.json');
        this.scheduledJobs = new Map();
        this.config = this.loadConfig();
        this.initializeReminders();
    }

    loadConfig() {
        return {
            timezone: process.env.timezone || 'Asia/Shanghai',
            defaultReminderAdvance: parseInt(process.env.default_reminder_advance) || 5,
            maxReminders: parseInt(process.env.max_reminders) || 100,
            enableEmail: process.env.enable_email === 'true',
            emailConfig: {
                host: process.env.email_smtp_host,
                port: parseInt(process.env.email_smtp_port) || 587,
                user: process.env.email_user,
                password: process.env.email_password
            },
            notificationSound: process.env.notification_sound !== 'false'
        };
    }

    async initializeReminders() {
        try {
            const reminders = await this.loadReminders();
            // 重新调度所有未过期的提醒
            for (const reminder of reminders) {
                if (moment(reminder.triggerTime).isAfter(moment())) {
                    this.scheduleReminder(reminder);
                }
            }
        } catch (error) {
            console.error('初始化提醒失败:', error);
        }
    }

    async loadReminders() {
        try {
            const data = await fs.readFile(this.remindersFile, 'utf8');
            return JSON.parse(data);
        } catch (error) {
            if (error.code === 'ENOENT') {
                return [];
            }
            throw error;
        }
    }

    async saveReminders(reminders) {
        await fs.writeFile(this.remindersFile, JSON.stringify(reminders, null, 2));
    }

    parseNaturalTime(timeExpression) {
        // 使用 chrono-node 解析自然语言时间
        const results = chrono.parse(timeExpression, new Date(), { forwardDate: true });
        
        if (results.length === 0) {
            // 如果 chrono 无法解析，尝试一些中文特定的模式
            return this.parseChineseTime(timeExpression);
        }

        const parsedDate = results[0].start.date();
        return moment(parsedDate);
    }

    parseChineseTime(timeExpression) {
        const now = moment();
        const text = timeExpression.toLowerCase().trim();

        // 处理相对时间表达式
        if (text.includes('明天')) {
            let time = now.clone().add(1, 'day');
            const timeMatch = text.match(/(\d{1,2})[点时](\d{1,2}分?)?/);
            if (timeMatch) {
                time.hour(parseInt(timeMatch[1]));
                time.minute(timeMatch[2] ? parseInt(timeMatch[2]) : 0);
            } else if (text.includes('上午') || text.includes('早上')) {
                time.hour(9).minute(0);
            } else if (text.includes('下午')) {
                time.hour(14).minute(0);
            } else if (text.includes('晚上')) {
                time.hour(19).minute(0);
            }
            return time;
        }

        if (text.includes('后天')) {
            let time = now.clone().add(2, 'day');
            const timeMatch = text.match(/(\d{1,2})[点时](\d{1,2}分?)?/);
            if (timeMatch) {
                time.hour(parseInt(timeMatch[1]));
                time.minute(timeMatch[2] ? parseInt(timeMatch[2]) : 0);
            }
            return time;
        }

        if (text.includes('下周')) {
            let time = now.clone().add(1, 'week');
            if (text.includes('一')) time.day(1);
            else if (text.includes('二')) time.day(2);
            else if (text.includes('三')) time.day(3);
            else if (text.includes('四')) time.day(4);
            else if (text.includes('五')) time.day(5);
            else if (text.includes('六')) time.day(6);
            else if (text.includes('日') || text.includes('天')) time.day(0);
            
            const timeMatch = text.match(/(\d{1,2})[点时](\d{1,2}分?)?/);
            if (timeMatch) {
                time.hour(parseInt(timeMatch[1]));
                time.minute(timeMatch[2] ? parseInt(timeMatch[2]) : 0);
            }
            return time;
        }

        // 处理 "X天后", "X小时后" 等
        const dayMatch = text.match(/(\d+)天后/);
        if (dayMatch) {
            return now.clone().add(parseInt(dayMatch[1]), 'day');
        }

        const hourMatch = text.match(/(\d+)小时后/);
        if (hourMatch) {
            return now.clone().add(parseInt(hourMatch[1]), 'hour');
        }

        const minuteMatch = text.match(/(\d+)分钟后/);
        if (minuteMatch) {
            return now.clone().add(parseInt(minuteMatch[1]), 'minute');
        }

        // 如果都无法解析，返回1小时后作为默认值
        return now.clone().add(1, 'hour');
    }

    generateReminderId() {
        return `reminder_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    async createReminder(reminderText, timeExpression, repeatType = 'once', notificationMethods = 'websocket') {
        const reminders = await this.loadReminders();
        
        if (reminders.length >= this.config.maxReminders) {
            throw new Error(`提醒数量已达上限 (${this.config.maxReminders})`);
        }

        const triggerTime = this.parseNaturalTime(timeExpression);
        
        if (triggerTime.isBefore(moment())) {
            throw new Error('提醒时间不能是过去的时间');
        }

        const reminder = {
            id: this.generateReminderId(),
            text: reminderText,
            originalTimeExpression: timeExpression,
            triggerTime: triggerTime.toISOString(),
            repeatType: repeatType,
            notificationMethods: notificationMethods.split(',').map(m => m.trim()),
            createdAt: moment().toISOString(),
            isActive: true
        };

        reminders.push(reminder);
        await this.saveReminders(reminders);
        
        // 调度提醒
        this.scheduleReminder(reminder);

        return {
            success: true,
            reminder: reminder,
            message: `提醒已设置：${reminderText}，将在 ${triggerTime.format('YYYY-MM-DD HH:mm')} 提醒您`
        };
    }

    scheduleReminder(reminder) {
        const triggerTime = moment(reminder.triggerTime);
        
        if (triggerTime.isBefore(moment())) {
            return; // 跳过过期的提醒
        }

        const job = schedule.scheduleJob(triggerTime.toDate(), async () => {
            await this.triggerReminder(reminder);
            
            // 处理重复提醒
            if (reminder.repeatType !== 'once') {
                await this.scheduleNextRepeat(reminder);
            } else {
                // 一次性提醒完成后标记为非活跃
                await this.markReminderInactive(reminder.id);
            }
        });

        this.scheduledJobs.set(reminder.id, job);
    }

    async triggerReminder(reminder) {
        const notification = {
            type: 'reminder_triggered',
            reminderId: reminder.id,
            text: reminder.text,
            triggerTime: reminder.triggerTime,
            timestamp: moment().toISOString(),
            sound: this.config.notificationSound
        };

        // WebSocket 通知（通过回调到主服务器）
        if (reminder.notificationMethods.includes('websocket')) {
            await this.sendWebSocketNotification(notification);
        }

        // 邮件通知
        if (reminder.notificationMethods.includes('email') && this.config.enableEmail) {
            await this.sendEmailNotification(reminder);
        }
    }

    async sendWebSocketNotification(notification) {
        try {
            const callbackUrl = process.env.CALLBACK_BASE_URL;
            if (callbackUrl) {
                const axios = require('axios');
                await axios.post(`${callbackUrl}/SmartReminder/notification`, notification);
            }
        } catch (error) {
            console.error('发送WebSocket通知失败:', error);
        }
    }

    async sendEmailNotification(reminder) {
        // 邮件发送逻辑（需要配置SMTP）
        if (!this.config.emailConfig.host || !this.config.emailConfig.user) {
            return;
        }

        try {
            const nodemailer = require('nodemailer');
            const transporter = nodemailer.createTransporter({
                host: this.config.emailConfig.host,
                port: this.config.emailConfig.port,
                secure: this.config.emailConfig.port === 465,
                auth: {
                    user: this.config.emailConfig.user,
                    pass: this.config.emailConfig.password
                }
            });

            await transporter.sendMail({
                from: this.config.emailConfig.user,
                to: this.config.emailConfig.user,
                subject: '智能提醒通知',
                text: `提醒内容：${reminder.text}\n提醒时间：${moment(reminder.triggerTime).format('YYYY-MM-DD HH:mm')}`
            });
        } catch (error) {
            console.error('发送邮件通知失败:', error);
        }
    }

    async listReminders(activeOnly = true) {
        const reminders = await this.loadReminders();
        const filtered = activeOnly ? reminders.filter(r => r.isActive) : reminders;
        
        return filtered.map(reminder => ({
            id: reminder.id,
            text: reminder.text,
            triggerTime: moment(reminder.triggerTime).format('YYYY-MM-DD HH:mm'),
            repeatType: reminder.repeatType,
            isActive: reminder.isActive,
            timeFromNow: moment(reminder.triggerTime).fromNow()
        }));
    }

    async deleteReminder(reminderId) {
        const reminders = await this.loadReminders();
        const index = reminders.findIndex(r => r.id === reminderId);
        
        if (index === -1) {
            throw new Error('提醒不存在');
        }

        // 取消调度的任务
        const job = this.scheduledJobs.get(reminderId);
        if (job) {
            job.cancel();
            this.scheduledJobs.delete(reminderId);
        }

        reminders.splice(index, 1);
        await this.saveReminders(reminders);

        return { success: true, message: '提醒已删除' };
    }

    async searchReminders(keyword) {
        const reminders = await this.loadReminders();
        const results = reminders.filter(r => 
            r.isActive && r.text.toLowerCase().includes(keyword.toLowerCase())
        );

        return results.map(reminder => ({
            id: reminder.id,
            text: reminder.text,
            triggerTime: moment(reminder.triggerTime).format('YYYY-MM-DD HH:mm'),
            timeFromNow: moment(reminder.triggerTime).fromNow()
        }));
    }

    async markReminderInactive(reminderId) {
        const reminders = await this.loadReminders();
        const reminder = reminders.find(r => r.id === reminderId);
        
        if (reminder) {
            reminder.isActive = false;
            await this.saveReminders(reminders);
        }
    }
}

// 主函数
async function main() {
    try {
        const input = process.stdin;
        let inputData = '';

        input.on('data', (chunk) => {
            inputData += chunk;
        });

        input.on('end', async () => {
            try {
                const params = JSON.parse(inputData.trim());
                const reminder = new SmartReminder();
                
                let result;
                
                switch (params.command) {
                    case 'create':
                        if (!params.reminder_text || !params.time_expression) {
                            throw new Error('缺少必需参数：reminder_text 和 time_expression');
                        }
                        result = await reminder.createReminder(
                            params.reminder_text,
                            params.time_expression,
                            params.repeat_type || 'once',
                            params.notification_methods || 'websocket'
                        );
                        break;
                        
                    case 'list':
                        const reminders = await reminder.listReminders();
                        result = {
                            success: true,
                            reminders: reminders,
                            count: reminders.length,
                            message: `找到 ${reminders.length} 个活跃提醒`
                        };
                        break;
                        
                    case 'delete':
                        if (!params.reminder_id) {
                            throw new Error('缺少必需参数：reminder_id');
                        }
                        result = await reminder.deleteReminder(params.reminder_id);
                        break;
                        
                    case 'search':
                        if (!params.keyword) {
                            throw new Error('缺少必需参数：keyword');
                        }
                        const searchResults = await reminder.searchReminders(params.keyword);
                        result = {
                            success: true,
                            reminders: searchResults,
                            count: searchResults.length,
                            message: `找到 ${searchResults.length} 个匹配的提醒`
                        };
                        break;
                        
                    default:
                        throw new Error(`未知命令：${params.command}`);
                }

                const response = {
                    status: 'success',
                    result: result,
                    messageForAI: result.message || '操作完成'
                };

                console.log(JSON.stringify(response, null, 2));
                
            } catch (error) {
                const errorResponse = {
                    status: 'error',
                    error: error.message,
                    messageForAI: `智能提醒操作失败：${error.message}`
                };
                console.log(JSON.stringify(errorResponse, null, 2));
            }
        });

    } catch (error) {
        const errorResponse = {
            status: 'error',
            error: error.message,
            messageForAI: `智能提醒系统错误：${error.message}`
        };
        console.log(JSON.stringify(errorResponse, null, 2));
    }
}

if (require.main === module) {
    main();
}

module.exports = SmartReminder;
