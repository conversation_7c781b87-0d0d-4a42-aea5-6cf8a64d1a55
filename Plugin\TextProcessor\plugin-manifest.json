{"manifestVersion": "1.0.0", "name": "TextProcessor", "version": "1.0.0", "displayName": "文本处理器", "description": "提供文本分析、转换和处理功能的多功能工具", "author": "VCP Assistant", "pluginType": "synchronous", "entryPoint": {"type": "python", "command": "python text_processor.py"}, "communication": {"protocol": "stdio", "timeout": 30000}, "configSchema": {"max_text_length": "integer", "default_language": "string", "enable_sentiment": "boolean"}, "capabilities": {"systemPromptPlaceholders": [], "invocationCommands": [{"commandIdentifier": "TextAnalysis", "description": "文本分析工具，支持多种文本处理功能：\n\n**支持的命令：**\n- `analyze`: 文本分析（字数统计、语言检测、情感分析）\n- `translate`: 文本翻译\n- `summarize`: 文本摘要\n- `extract_keywords`: 关键词提取\n- `format`: 文本格式化\n\n**调用格式：**\n```\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」TextProcessor「末」,\ncommand:「始」命令类型「末」,\ntext:「始」要处理的文本内容「末」,\ntarget_language:「始」目标语言（翻译时使用）「末」,\nformat_type:「始」格式类型（格式化时使用）「末」\n<<<[END_TOOL_REQUEST]>>>\n```", "example": "```\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」TextProcessor「末」,\ncommand:「始」analyze「末」,\ntext:「始」这是一段需要分析的中文文本，包含了情感色彩和关键信息。「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}], "responseFormatToAI": "### 文本处理结果\n{result}"}, "dependencies": {"python": ">=3.7", "libraries": ["ji<PERSON>a", "textstat", "langdetect"]}}